{"cells": [{"cell_type": "code", "execution_count": null, "id": "d8f460a5", "metadata": {}, "outputs": [], "source": ["from ultralytics import YOLO\n", "import cv2\n", "import numpy as np\n", "\n", "# Load YOLOv8 model\n", "model = YOLO('../models/yolov8n.pt')  # Use 'yolov8s.pt' etc. as needed\n", "\n", "cap = cv2.VideoCapture(1)\n", "\n", "ret, frame1 = cap.read()\n", "ret2, frame2 = cap.read()\n", "\n", "if not ret or not ret2:\n", "    print(\"Failed to grab initial frames.\")\n", "    cap.release()\n", "    exit()\n", "\n", "while cap.isOpened():\n", "    diff = cv2.absdiff(frame1, frame2)\n", "    gray = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)\n", "    blur = cv2.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(gray, (5, 5), 0)\n", "    _, thresh = cv2.threshold(blur, 20, 255, cv2.THRESH_BINARY)\n", "    dilated = cv2.dilate(thresh, None, iterations=3)\n", "    contours, _ = cv2.findContours(dilated, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)\n", "\n", "    motion_detected = False\n", "    for contour in contours:\n", "        if cv2.contourArea(contour) < 1000:\n", "            continue\n", "        motion_detected = True\n", "        (x, y, w, h) = cv2.boundingRect(contour)\n", "        cv2.rectangle(frame1, (x, y), (x + w, y + h), (0, 255, 0), 2)\n", "\n", "    if motion_detected:\n", "        # Run YOLOv8 on the frame\n", "        results = model(frame1, verbose=False)\n", "        annotated_frame = results[0].plot()\n", "        cv2.imshow(\"Motion + YOLO\", annotated_frame)\n", "    else:\n", "        cv2.imshow(\"Motion + YOLO\", frame1)\n", "\n", "    frame1 = frame2\n", "    ret, frame2 = cap.read()\n", "    if not ret:\n", "        break\n", "\n", "    if cv2.wait<PERSON><PERSON>(10) == 27:\n", "        break\n", "\n", "cap.release()\n", "cv2.destroyAllWindows()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}